const https = require('https');

// Configurações - substitua pelos seus tokens de autorização
const GET_AUTH_TOKEN = 'Bearer dG9rOjA2NTQxNmQ4XzNjNWNfNGFlMF85NjQ1XzRmMzVjMzM0MzRhYzoxOjA=';
const POST_AUTH_TOKEN = 'Bearer dG9rOmFhNDdlMDg2X2Y1ODNfNGUxMF9iNTc0XzkyYWM0MzM2ZmUxOToxOjA=';

// Configuração de substituição de texto
const TEXT_REPLACEMENTS = {
  'Friday': 'GigU'  // Substitua 'NOVO_NOME_AQUI' pelo nome desejado
};

// Configuração manual do Help Center ID (opcional)
// Se definido, será usado em vez de buscar automaticamente
const MANUAL_HELP_CENTER_ID = null; // Exemplo: 12345

// Configuração do Author ID para o ambiente de destino
// Substitua pelo ID de um admin/autor válido no ambiente de destino
const DEFAULT_AUTHOR_ID = 5469516; // SUBSTITUA pelo ID do autor correto

// IDs dos artigos para migrar
const articleIds = [
  // "6161949",
  // "6467978",
  "6125691",
  "11471588",
  "6085002",
  "6229423",
  "5794929",
  "6387490",
  "8776428",
  "5905095",
  "8167431",
  "5992676",
  "5952016",
  "5952023",
  "5952018",
  "7264780",
  "8081614",
  "8148934",
  "8313475",
  "8981555"
];

// Mapa para armazenar collections criadas (título -> novo ID)
const collectionsMap = new Map();

// Função para aplicar substituições de texto
function applyTextReplacements(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  let processedText = text;

  for (const [oldText, newText] of Object.entries(TEXT_REPLACEMENTS)) {
    // Substituição global case-insensitive, mas preservando a capitalização original
    const regex = new RegExp(oldText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    processedText = processedText.replace(regex, (match) => {
      // Preservar a capitalização do texto original
      if (match === match.toUpperCase()) {
        return newText.toUpperCase();
      } else if (match === match.toLowerCase()) {
        return newText.toLowerCase();
      } else if (match[0] === match[0].toUpperCase()) {
        return newText.charAt(0).toUpperCase() + newText.slice(1).toLowerCase();
      }
      return newText;
    });
  }

  return processedText;
}

// Função para processar um artigo e aplicar substituições
function processArticleContent(article) {
  const processedArticle = { ...article };

  // Aplicar substituições nos campos de texto
  if (processedArticle.title) {
    processedArticle.title = applyTextReplacements(processedArticle.title);
  }

  if (processedArticle.description) {
    processedArticle.description = applyTextReplacements(processedArticle.description);
  }

  if (processedArticle.body) {
    processedArticle.body = applyTextReplacements(processedArticle.body);
  }

  return processedArticle;
}

// Função para fazer requisição GET genérica
function makeGetRequest(path, description) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.intercom.io',
      path: path,
      method: 'GET',
      headers: {
        'Authorization': GET_AUTH_TOKEN,
        'Accept': 'application/json',
        'Intercom-Version': '2.14'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: result });
        } catch (error) {
          reject(new Error(`Erro ao parsear JSON para ${description}: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Erro na requisição GET para ${description}: ${error.message}`));
    });

    req.end();
  });
}

// Função para fazer requisição GET de artigo
function getArticle(articleId) {
  return makeGetRequest(`/articles/${articleId}`, `artigo ${articleId}`)
    .then(response => {
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.data;
      } else {
        throw new Error(`HTTP ${response.statusCode} ao buscar artigo ${articleId}`);
      }
    });
}

// Função para listar todas as collections (usando token de POST - ambiente destino)
function getAllCollections() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.intercom.io',
      path: '/help_center/collections',
      method: 'GET',
      headers: {
        'Authorization': POST_AUTH_TOKEN,  // Usar token de POST para ambiente destino
        'Accept': 'application/json',
        'Intercom-Version': '2.14'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(result.data || []);
          } else {
            reject(new Error(`HTTP ${res.statusCode} ao buscar collections`));
          }
        } catch (error) {
          reject(new Error(`Erro ao parsear resposta das collections: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Erro na requisição para collections: ${error.message}`));
    });

    req.end();
  });
}

// Função para buscar admins disponíveis (usando token de POST - ambiente destino)
function getAdmins() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.intercom.io',
      path: '/admins',
      method: 'GET',
      headers: {
        'Authorization': POST_AUTH_TOKEN,
        'Accept': 'application/json',
        'Intercom-Version': '2.14'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(result.data || []);
          } else {
            console.log(`❌ Erro ${res.statusCode} ao buscar admins do ambiente destino:`);
            console.log(`📄 Resposta:`, JSON.stringify(result, null, 2));
            reject(new Error(`HTTP ${res.statusCode} ao buscar admins`));
          }
        } catch (error) {
          reject(new Error(`Erro ao parsear resposta dos admins: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Erro na requisição para admins: ${error.message}`));
    });

    req.end();
  });
}

// Função para buscar help centers disponíveis (usando token de POST - ambiente destino)
function getHelpCenters() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.intercom.io',
      path: '/help_center/help_centers',
      method: 'GET',
      headers: {
        'Authorization': POST_AUTH_TOKEN,  // Usar token de POST para ambiente destino
        'Accept': 'application/json',
        'Intercom-Version': '2.14'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(result.data || []);
          } else {
            console.log(`❌ Erro ${res.statusCode} ao buscar help centers do ambiente destino:`);
            console.log(`📄 Resposta:`, JSON.stringify(result, null, 2));
            reject(new Error(`HTTP ${res.statusCode} ao buscar help centers`));
          }
        } catch (error) {
          reject(new Error(`Erro ao parsear resposta dos help centers: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Erro na requisição para help centers: ${error.message}`));
    });

    req.end();
  });
}

// Função para fazer requisição POST genérica
function makePostRequest(path, data, description) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);

    const options = {
      hostname: 'api.intercom.io',
      path: path,
      method: 'POST',
      headers: {
        'Authorization': POST_AUTH_TOKEN,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Intercom-Version': '2.14',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);

          // Log detalhado para erros 4xx
          if (res.statusCode >= 400 && res.statusCode < 500) {
            console.log(`❌ Erro ${res.statusCode} ao criar ${description}:`);
            console.log(`📄 Resposta completa:`, JSON.stringify(result, null, 2));
          }

          resolve({ statusCode: res.statusCode, data: result });
        } catch (error) {
          console.log(`❌ Erro ao parsear resposta para ${description}:`);
          console.log(`📄 Resposta raw:`, data);
          reject(new Error(`Erro ao parsear resposta do POST para ${description}: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Erro na requisição POST para ${description}: ${error.message}`));
    });

    req.write(postData);
    req.end();
  });
}

// Função para validar dados da collection
function validateCollectionData(data) {
  const errors = [];

  if (!data.name || typeof data.name !== 'string' || data.name.trim() === '') {
    errors.push('Nome da collection é obrigatório');
  }

  if (!data.help_center_id) {
    errors.push('help_center_id é obrigatório');
  } else if (typeof data.help_center_id !== 'number' || isNaN(data.help_center_id)) {
    errors.push(`help_center_id deve ser um número válido (recebido: ${data.help_center_id}, tipo: ${typeof data.help_center_id})`);
  }

  if (data.parent_id && typeof data.parent_id !== 'number') {
    errors.push('parent_id deve ser um número');
  }

  return errors;
}

// Função para criar uma collection
function createCollection(collectionData) {
  const data = {
    name: collectionData.name,
    description: collectionData.description || '',
    parent_id: collectionData.parent_id || null,
    help_center_id: collectionData.help_center_id
  };

  // Validar dados antes de enviar
  const validationErrors = validateCollectionData(data);
  if (validationErrors.length > 0) {
    console.log(`❌ Erros de validação para collection "${collectionData.name}":`);
    validationErrors.forEach(error => console.log(`   - ${error}`));
    return Promise.resolve({
      statusCode: 400,
      data: { errors: validationErrors }
    });
  }

  // Log detalhado dos dados sendo enviados
  console.log(`🔍 Dados da collection sendo criada:`);
  console.log(`   - name: "${data.name}" (${typeof data.name})`);
  console.log(`   - description: "${data.description}" (${typeof data.description})`);
  console.log(`   - parent_id: ${data.parent_id} (${typeof data.parent_id})`);
  console.log(`   - help_center_id: ${data.help_center_id} (${typeof data.help_center_id})`);

  return makePostRequest('/help_center/collections', data, `collection "${collectionData.name}"`);
}

// Função auxiliar para buscar nome da collection pelo ID original
async function getCollectionNameById(collectionId) {
  try {
    const response = await makeGetRequest(`/help_center/collections/${collectionId}`, `collection ${collectionId}`);
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return response.data.name;
    }
  } catch (error) {
    console.log(`⚠️  Erro ao buscar collection ${collectionId}: ${error.message}`);
  }
  return null;
}

// Função para fazer requisição POST de artigo
async function postArticle(articleData, authorId, collectionsMap, existingCollections) {
  // Mapear parent_id se for uma collection
  let parentId = articleData.parent_id;
  let parentCollectionName = null;

  if (articleData.parent_type === 'collection' && articleData.parent_id) {
    console.log(`🔍 Mapeando collection para artigo "${articleData.title}"...`);
    console.log(`   Collection original ID: ${articleData.parent_id}`);

    // Buscar o novo ID da collection no mapa usando o ID original
    const parentCollection = [...collectionsMap.entries()]
      .find(([title, data]) => data.originalId === articleData.parent_id);

    if (parentCollection) {
      parentCollectionName = parentCollection[0];
      parentId = parentCollection[1].newId;
      console.log(`✅ Collection mapeada: "${parentCollectionName}" (${articleData.parent_id} → ${parentId})`);
    } else {
      // Tentar buscar a collection original para obter o nome
      console.log(`🔍 Collection não encontrada no mapa. Tentando buscar dados da collection original...`);

      try {
        const originalCollectionName = await getCollectionNameById(articleData.parent_id);
        if (originalCollectionName && existingCollections.has(originalCollectionName)) {
          const existingCollection = existingCollections.get(originalCollectionName);
          parentId = existingCollection.id;
          parentCollectionName = originalCollectionName;
          console.log(`✅ Collection encontrada nas existentes: "${originalCollectionName}" (ID: ${parentId})`);
        } else {
          console.log(`❌ ERRO: Collection com ID original ${articleData.parent_id} não foi encontrada em lugar nenhum`);
          console.log(`📋 Collections no mapa:`, [...collectionsMap.entries()].map(([name, data]) => `"${name}" (${data.originalId} → ${data.newId})`));
          console.log(`📋 Collections existentes:`, [...existingCollections.entries()].map(([name, data]) => `"${name}" (ID: ${data.id})`));

          // Definir parentId como null para evitar erro "collection not found"
          parentId = null;
          console.log(`⚠️  Definindo parent_id como null para evitar erro`);
        }
      } catch (error) {
        console.log(`❌ Erro ao buscar collection original: ${error.message}`);
        parentId = null;
        console.log(`⚠️  Definindo parent_id como null para evitar erro`);
      }
    }
  }

  const data = {
    title: articleData.title,
    author_id: authorId || DEFAULT_AUTHOR_ID, // Usar author_id do ambiente de destino
    description: articleData.description,
    body: articleData.body,
    state: articleData.state,
    parent_id: parentId,
    parent_type: articleData.parent_type
  };

  return makePostRequest('/articles', data, `artigo "${articleData.title}"`);
}

// Função para verificar se uma collection já existe pelo título
async function checkExistingCollections(helpCenterId) {
  try {
    console.log('🔍 Verificando collections existentes no ambiente de destino...');
    const existingCollections = await getAllCollections();
    const collectionsMap = new Map();

    existingCollections.forEach(collection => {
      collectionsMap.set(collection.name, {
        id: collection.id,
        name: collection.name,
        parent_id: collection.parent_id,
        help_center_id: collection.help_center_id
      });
    });

    console.log(`✅ Encontradas ${existingCollections.length} collections existentes`);
    if (existingCollections.length > 0) {
      console.log('📋 Collections existentes:');
      existingCollections.forEach(col => {
        console.log(`   - "${col.name}" (ID: ${col.id})`);
      });
    }

    return collectionsMap;
  } catch (error) {
    console.log(`⚠️  Erro ao verificar collections existentes: ${error.message}`);
    return new Map();
  }
}

// Função para buscar artigos existentes no ambiente de destino
async function checkExistingArticles() {
  try {
    console.log('🔍 Verificando artigos existentes no ambiente de destino...');

    const response = await new Promise((resolve, reject) => {
      const options = {
        hostname: 'api.intercom.io',
        path: '/articles',
        method: 'GET',
        headers: {
          'Authorization': POST_AUTH_TOKEN,  // Usar token de POST para ambiente destino
          'Accept': 'application/json',
          'Intercom-Version': '2.14'
        }
      };

      const req = https.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(result.data || []);
            } else {
              reject(new Error(`HTTP ${res.statusCode} ao buscar artigos`));
            }
          } catch (error) {
            reject(new Error(`Erro ao parsear resposta dos artigos: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(new Error(`Erro na requisição para artigos: ${error.message}`));
      });

      req.end();
    });

    const articlesMap = new Map();
    response.forEach(article => {
      articlesMap.set(article.title, {
        id: article.id,
        title: article.title,
        state: article.state,
        parent_id: article.parent_id,
        parent_type: article.parent_type
      });
    });

    console.log(`✅ Encontrados ${response.length} artigos existentes`);
    if (response.length > 0) {
      console.log('📋 Primeiros 5 artigos existentes:');
      response.slice(0, 5).forEach(article => {
        console.log(`   - "${article.title}" (ID: ${article.id})`);
      });
      if (response.length > 5) {
        console.log(`   ... e mais ${response.length - 5} artigos`);
      }
    }

    return articlesMap;
  } catch (error) {
    console.log(`⚠️  Erro ao verificar artigos existentes: ${error.message}`);
    return new Map();
  }
}

// Função para processar collections e criar hierarquia
async function processCollections(articles) {
  console.log('📁 Processando collections...\n');

  // Determinar o help_center_id a ser usado
  let defaultHelpCenterId = MANUAL_HELP_CENTER_ID ? parseInt(MANUAL_HELP_CENTER_ID) : null;

  if (defaultHelpCenterId) {
    console.log(`🔧 Usando Help Center ID manual: ${defaultHelpCenterId} (tipo: ${typeof defaultHelpCenterId})`);
  } else {
    // Buscar help centers disponíveis no ambiente de destino
    try {
      console.log('🔍 Buscando help centers do ambiente de destino (usando token POST)...');
      const helpCenters = await getHelpCenters();
      if (helpCenters.length > 0) {
        defaultHelpCenterId = parseInt(helpCenters[0].id);
        console.log(`✅ Help Center do destino encontrado: ID ${defaultHelpCenterId} (tipo: ${typeof defaultHelpCenterId})`);
        if (helpCenters.length > 1) {
          console.log(`📋 Help Centers disponíveis no destino:`);
          helpCenters.forEach((hc, index) => {
            console.log(`   ${index + 1}. ID: ${hc.id} - ${hc.display_name || 'Sem nome'}`);
          });
        }
      } else {
        console.log('⚠️  Nenhum help center encontrado no ambiente de destino');
      }
    } catch (error) {
      console.log(`⚠️  Erro ao buscar help centers do destino: ${error.message}`);
      console.log(`💡 Verifique se o token POST tem permissões para acessar help centers`);
      console.log(`💡 Ou configure MANUAL_HELP_CENTER_ID no script`);
    }
  }

  // Verificar collections existentes no ambiente de destino
  const existingCollections = await checkExistingCollections(defaultHelpCenterId);

  // Extrair todas as collections únicas dos artigos
  const collectionsNeeded = new Map();

  for (const article of articles) {
    if (article.parent_type === 'collection' && article.parent_id) {
      // Buscar dados da collection original
      try {
        console.log(`🔍 Buscando collection ${article.parent_id}...`);
        const collectionResponse = await makeGetRequest(`/help_center/collections/${article.parent_id}`, `collection ${article.parent_id}`);

        if (collectionResponse.statusCode >= 200 && collectionResponse.statusCode < 300) {
          const collection = collectionResponse.data;
          collectionsNeeded.set(collection.name, {
            originalId: collection.id,
            name: collection.name,
            description: collection.description,
            parent_id: collection.parent_id,
            help_center_id: collection.help_center_id
          });
          console.log(`✅ Collection encontrada: "${collection.name}" (ID original: ${collection.id}, parent_id: ${collection.parent_id || 'nenhum'})`);

          // Se tem parent, buscar dados do parent também
          if (collection.parent_id && !collectionsNeeded.has(`parent_${collection.parent_id}`)) {
            try {
              console.log(`🔍 Buscando collection pai ${collection.parent_id}...`);
              const parentResponse = await makeGetRequest(`/help_center/collections/${collection.parent_id}`, `collection pai ${collection.parent_id}`);

              if (parentResponse.statusCode >= 200 && parentResponse.statusCode < 300) {
                const parentCollection = parentResponse.data;
                collectionsNeeded.set(parentCollection.name, {
                  originalId: parentCollection.id,
                  name: parentCollection.name,
                  description: parentCollection.description,
                  parent_id: parentCollection.parent_id,
                  help_center_id: parentCollection.help_center_id
                });
                console.log(`✅ Collection pai encontrada: "${parentCollection.name}" (ID original: ${parentCollection.id})`);
              }
            } catch (parentError) {
              console.log(`⚠️  Erro ao buscar collection pai ${collection.parent_id}: ${parentError.message}`);
            }
          }
        }
      } catch (error) {
        console.log(`⚠️  Erro ao buscar collection ${article.parent_id}: ${error.message}`);
      }
    }
  }

  // Ordenar collections por hierarquia (pais primeiro)
  const sortedCollections = [...collectionsNeeded.values()].sort((a, b) => {
    // Collections sem parent vêm primeiro
    if (!a.parent_id && b.parent_id) return -1;
    if (a.parent_id && !b.parent_id) return 1;

    // Se ambas têm parent, verificar se uma é pai da outra
    if (a.parent_id && b.parent_id) {
      // Se A é pai de B, A vem primeiro
      if (a.originalId === b.parent_id) return -1;
      // Se B é pai de A, B vem primeiro
      if (b.originalId === a.parent_id) return 1;
    }

    return 0;
  });

  console.log(`📋 Ordem de criação das collections:`);
  sortedCollections.forEach((col, index) => {
    console.log(`  ${index + 1}. "${col.name}" (parent_id: ${col.parent_id || 'nenhum'})`);
  });

  // Criar collections na ordem correta (com múltiplas tentativas se necessário)
  let remainingCollections = [...sortedCollections];
  let maxAttempts = 3;
  let attempt = 1;

  while (remainingCollections.length > 0 && attempt <= maxAttempts) {
    console.log(`\n🔄 Tentativa ${attempt} - Collections restantes: ${remainingCollections.length}`);
    const collectionsToRetry = [];

    for (const collectionData of remainingCollections) {
    try {
      // Verificar se a collection já existe no ambiente de destino
      if (existingCollections.has(collectionData.name)) {
        const existingCollection = existingCollections.get(collectionData.name);
        console.log(`♻️  Collection "${collectionData.name}" já existe (ID: ${existingCollection.id}). Reutilizando...`);

        collectionsMap.set(collectionData.name, {
          originalId: collectionData.originalId,
          newId: existingCollection.id
        });
        continue; // Pular criação, usar a existente
      }

      console.log(`📝 Criando collection "${collectionData.name}"...`);

      // Verificar se a collection pai já foi criada
      let parentId = null;
      if (collectionData.parent_id) {
        const parentCollection = [...collectionsMap.entries()]
          .find(([title, data]) => data.originalId === collectionData.parent_id);

        if (parentCollection) {
          parentId = parentCollection[1].newId;
          console.log(`🔗 Mapeando parent: ${collectionData.parent_id} → ${parentId}`);
        } else {
          console.log(`⏳ Collection pai (ID: ${collectionData.parent_id}) ainda não foi criada para "${collectionData.name}". Tentando depois...`);
          collectionsToRetry.push(collectionData);
          continue; // Pular esta collection por enquanto
        }
      }

      const createData = {
        name: collectionData.name,
        description: collectionData.description,
        parent_id: parentId,
        help_center_id: parseInt(defaultHelpCenterId)  // Converter para número
      };

      // Validar se temos help_center_id
      if (!createData.help_center_id) {
        console.log(`❌ Erro: help_center_id do ambiente de destino não encontrado para collection "${collectionData.name}"`);
        console.log(`💡 Certifique-se de que o token POST tem acesso a um help center`);
        continue;
      }

      const result = await createCollection(createData);

      if (result.statusCode >= 200 && result.statusCode < 300) {
        collectionsMap.set(collectionData.name, {
          originalId: collectionData.originalId,
          newId: result.data.id
        });
        console.log(`✅ Collection "${collectionData.name}" criada com ID ${result.data.id}`);
      } else {
        console.log(`❌ Erro ao criar collection "${collectionData.name}": HTTP ${result.statusCode}`);
        if (result.data && result.data.errors) {
          console.log(`📄 Detalhes do erro:`, JSON.stringify(result.data.errors, null, 2));
        }
        collectionsToRetry.push(collectionData);
      }

    } catch (error) {
      console.log(`❌ Erro ao criar collection "${collectionData.name}": ${error.message}`);
      collectionsToRetry.push(collectionData);
    }

    // Pausa entre criações
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

    remainingCollections = collectionsToRetry;
    attempt++;
  }

  if (remainingCollections.length > 0) {
    console.log(`\n⚠️  ${remainingCollections.length} collections não puderam ser criadas após ${maxAttempts} tentativas:`);
    remainingCollections.forEach(col => {
      console.log(`   - "${col.name}" (parent_id: ${col.parent_id})`);
    });
  }

  console.log(`\n📊 Collections processadas: ${collectionsMap.size} criadas\n`);

  return existingCollections;
}

// Função principal de migração
async function migrateArticles() {
  console.log('🚀 Iniciando migração de artigos da Intercom...\n');

  // Verificar/buscar author_id se não configurado manualmente
  let authorId = DEFAULT_AUTHOR_ID;

  if (!authorId) {
    try {
      console.log('🔍 Buscando admins do ambiente de destino para usar como autor...');
      const admins = await getAdmins();
      if (admins.length > 0) {
        authorId = parseInt(admins[0].id);
        console.log(`✅ Admin encontrado para usar como autor: ID ${authorId} - ${admins[0].name || 'Sem nome'}`);
        if (admins.length > 1) {
          console.log(`📋 Outros admins disponíveis:`);
          admins.slice(1, 3).forEach((admin, index) => {
            console.log(`   ${index + 2}. ID: ${admin.id} - ${admin.name || 'Sem nome'}`);
          });
        }
      } else {
        console.log('❌ Nenhum admin encontrado no ambiente de destino');
        console.log('💡 Configure DEFAULT_AUTHOR_ID manualmente no script');
        return;
      }
    } catch (error) {
      console.log(`❌ Erro ao buscar admins: ${error.message}`);
      console.log('💡 Configure DEFAULT_AUTHOR_ID manualmente no script');
      return;
    }
  } else {
    console.log(`🔧 Usando Author ID configurado: ${authorId}`);
  }

  const results = {
    success: [],
    errors: [],
    collections: [],
    authorId: authorId
  };

  // Primeiro, buscar todos os artigos para identificar collections
  const articles = [];
  console.log('🔍 Buscando todos os artigos para identificar collections...\n');

  for (const articleId of articleIds) {
    try {
      console.log(`📖 Buscando artigo ${articleId}...`);
      const article = await getArticle(articleId);
      articles.push(article);
      console.log(`✅ Artigo ${articleId} obtido: "${article.title}"`);

      // Pequena pausa entre requisições
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      console.log(`❌ Erro ao buscar artigo ${articleId}: ${error.message}`);
      results.errors.push({
        articleId,
        error: error.message,
        phase: 'fetch'
      });
    }
  }

  // Processar e criar collections necessárias
  let existingCollections = new Map();
  if (articles.length > 0) {
    existingCollections = await processCollections(articles);
  }

  // Verificar artigos existentes no ambiente de destino
  const existingArticles = await checkExistingArticles();

  // Agora migrar os artigos
  console.log('📝 Iniciando migração dos artigos...\n');

  for (const article of articles) {
    try {
      // Aplicar substituições de texto
      const processedArticle = processArticleContent(article);

      // Verificar se o artigo já existe no ambiente de destino
      if (existingArticles.has(processedArticle.title)) {
        const existingArticle = existingArticles.get(processedArticle.title);
        console.log(`♻️  Artigo "${processedArticle.title}" já existe (ID: ${existingArticle.id}). Pulando...`);

        results.success.push({
          originalId: article.id,
          originalTitle: article.title,
          processedTitle: processedArticle.title,
          newId: existingArticle.id,
          replacementsApplied: false,
          skipped: true
        });
        continue; // Pular criação, artigo já existe
      }

      // Log das substituições aplicadas
      const replacementsApplied = [];
      for (const [oldText, newText] of Object.entries(TEXT_REPLACEMENTS)) {
        const regex = new RegExp(oldText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        if (regex.test(article.title) || regex.test(article.description || '') || regex.test(article.body || '')) {
          replacementsApplied.push(`"${oldText}" → "${newText}"`);
        }
      }

      if (replacementsApplied.length > 0) {
        console.log(`🔄 Substituições aplicadas: ${replacementsApplied.join(', ')}`);
      }

      console.log(`📝 Publicando artigo "${processedArticle.title}"...`);
      const postResult = await postArticle(processedArticle, authorId, collectionsMap, existingCollections);

      if (postResult.statusCode >= 200 && postResult.statusCode < 300) {
        console.log(`✅ Artigo "${processedArticle.title}" publicado com sucesso!`);
        results.success.push({
          originalId: article.id,
          originalTitle: article.title,
          processedTitle: processedArticle.title,
          newId: postResult.data.id,
          replacementsApplied: replacementsApplied.length > 0
        });
      } else {
        console.log(`❌ Erro ao publicar artigo "${processedArticle.title}". Status: ${postResult.statusCode}`);
        results.errors.push({
          articleId: article.id,
          originalTitle: article.title,
          processedTitle: processedArticle.title,
          error: `HTTP ${postResult.statusCode}`,
          response: postResult.data,
          phase: 'publish'
        });
      }

      console.log('---');

      // Pequena pausa entre requisições para evitar rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.log(`❌ Erro ao processar artigo "${processedArticle.title || article.title}": ${error.message}`);
      results.errors.push({
        articleId: article.id,
        originalTitle: article.title,
        processedTitle: processedArticle?.title,
        error: error.message,
        phase: 'publish'
      });
      console.log('---');
    }
  }

  // Adicionar informações das collections ao resultado
  results.collections = [...collectionsMap.entries()].map(([title, data]) => ({
    title,
    originalId: data.originalId,
    newId: data.newId
  }));

  // Relatório final
  console.log('\n📊 RELATÓRIO FINAL DA MIGRAÇÃO');
  console.log('================================');
  console.log(`👤 Author ID usado: ${results.authorId}`);
  console.log(`📁 Collections criadas: ${results.collections.length}`);
  console.log(`✅ Artigos migrados com sucesso: ${results.success.length}`);
  console.log(`❌ Artigos com erro: ${results.errors.length}`);

  // Contar artigos com substituições e pulados
  const articlesWithReplacements = results.success.filter(item => item.replacementsApplied).length;
  const articlesSkipped = results.success.filter(item => item.skipped).length;
  const articlesCreated = results.success.filter(item => !item.skipped).length;

  if (articlesWithReplacements > 0) {
    console.log(`🔄 Artigos com substituições de texto: ${articlesWithReplacements}`);
  }
  if (articlesSkipped > 0) {
    console.log(`♻️  Artigos já existentes (pulados): ${articlesSkipped}`);
  }
  console.log(`🆕 Artigos criados: ${articlesCreated}`);

  if (results.collections.length > 0) {
    console.log('\n📁 COLLECTIONS CRIADAS:');
    results.collections.forEach(item => {
      console.log(`  - "${item.title}": ${item.originalId} → ${item.newId}`);
    });
  }

  if (results.success.length > 0) {
    console.log('\n🎉 ARTIGOS PROCESSADOS:');
    results.success.forEach(item => {
      const replacementIndicator = item.replacementsApplied ? ' 🔄' : '';
      const skippedIndicator = item.skipped ? ' ♻️' : '';
      const titleDisplay = item.originalTitle !== item.processedTitle
        ? `"${item.originalTitle}" → "${item.processedTitle}"`
        : `"${item.processedTitle}"`;
      const status = item.skipped ? 'já existia' : 'criado';
      console.log(`  - ${item.originalId} → ${item.newId}: ${titleDisplay} (${status})${replacementIndicator}${skippedIndicator}`);
    });
  }

  if (results.errors.length > 0) {
    console.log('\n💥 ERROS:');
    results.errors.forEach(item => {
      const phase = item.phase ? ` (${item.phase})` : '';
      console.log(`  - ${item.articleId || 'N/A'}${phase}: ${item.error}`);
      if (item.originalTitle) {
        const titleDisplay = item.originalTitle !== item.processedTitle && item.processedTitle
          ? `"${item.originalTitle}" → "${item.processedTitle}"`
          : `"${item.originalTitle}"`;
        console.log(`    Título: ${titleDisplay}`);
      }
      if (item.response) {
        console.log(`    Resposta: ${JSON.stringify(item.response, null, 2)}`);
      }
    });
  }

  console.log('\n🏁 Migração finalizada!');
}

// Executar o script
if (require.main === module) {
  migrateArticles().catch(error => {
    console.error('💥 Erro fatal na migração:', error);
    process.exit(1);
  });
}

module.exports = {
  migrateArticles,
  getArticle,
  postArticle,
  createCollection,
  getAllCollections,
  getHelpCenters,
  getAdmins,
  processCollections,
  checkExistingCollections,
  checkExistingArticles,
  applyTextReplacements,
  processArticleContent,
  validateCollectionData
};
